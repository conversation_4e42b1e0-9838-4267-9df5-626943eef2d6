'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { TrackingIcon } from '@/components/icons/ShippingIcons';

interface TrackingFormProps {
  onTrack: (trackingId: string) => void;
}

export const TrackingForm: React.FC<TrackingFormProps> = ({ onTrack }) => {
  const [trackingId, setTrackingId] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!trackingId.trim()) return;
    
    setIsLoading(true);
    // Simulate API call
    setTimeout(() => {
      onTrack(trackingId.trim());
      setIsLoading(false);
    }, 1000);
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-8 max-w-2xl mx-auto">
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-100 text-primary-600 rounded-full mb-4">
          <TrackingIcon size={32} />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Track Your Shipment</h2>
        <p className="text-gray-600">Enter your tracking ID to get real-time updates on your package</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label htmlFor="trackingId" className="block text-sm font-medium text-gray-700 mb-2">
            Tracking ID
          </label>
          <input
            type="text"
            id="trackingId"
            value={trackingId}
            onChange={(e) => setTrackingId(e.target.value)}
            placeholder="Enter tracking ID (e.g., VT-2024-EG-001234)"
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
            required
          />
        </div>

        <Button 
          type="submit" 
          variant="primary" 
          size="lg" 
          className="w-full"
          disabled={isLoading || !trackingId.trim()}
        >
          {isLoading ? (
            <div className="flex items-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Tracking...
            </div>
          ) : (
            'Track Package'
          )}
        </Button>
      </form>

      <div className="mt-6 text-center">
        <p className="text-sm text-gray-500">
          Don't have a tracking ID? 
          <a href="/contact" className="text-primary-600 hover:text-primary-700 ml-1">Contact us</a>
        </p>
      </div>
    </div>
  );
};
