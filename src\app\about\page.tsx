import React from 'react';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';
import { Card, CardContent } from '@/components/ui/Card';
import { PyramidIcon, TruckIcon, LocationIcon } from '@/components/icons/ShippingIcons';

export default function AboutPage() {
  const stats = [
    { number: '10,000+', label: 'Packages Delivered', icon: '📦' },
    { number: '50+', label: 'Cities Covered', icon: '🏙️' },
    { number: '99%', label: 'On-Time Delivery', icon: '⏰' },
    { number: '5+', label: 'Years of Excellence', icon: '🏆' }
  ];

  const team = [
    {
      name: '<PERSON>',
      role: 'Founder & CEO',
      description: 'Logistics expert with 15+ years in Egyptian shipping industry',
      image: '👨‍💼'
    },
    {
      name: '<PERSON><PERSON>',
      role: 'Operations Director',
      description: 'Ensures smooth operations across all Egyptian governorates',
      image: '👩‍💼'
    },
    {
      name: '<PERSON>',
      role: 'Technology Lead',
      description: 'Drives innovation in tracking and logistics technology',
      image: '👨‍💻'
    },
    {
      name: '<PERSON><PERSON>',
      role: 'Customer Success Manager',
      description: 'Dedicated to providing exceptional customer experiences',
      image: '👩‍🎓'
    }
  ];

  const values = [
    {
      title: 'Reliability',
      description: 'We deliver on our promises, ensuring your packages reach their destination safely and on time.',
      icon: '🛡️'
    },
    {
      title: 'Innovation',
      description: 'Leveraging cutting-edge technology to provide the best shipping experience in Egypt.',
      icon: '💡'
    },
    {
      title: 'Local Expertise',
      description: 'Deep understanding of Egyptian markets, culture, and logistics challenges.',
      icon: '🇪🇬'
    },
    {
      title: 'Customer First',
      description: 'Every decision we make is centered around providing exceptional customer service.',
      icon: '❤️'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main>
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-primary-600 to-primary-800 text-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div>
                <h1 className="text-4xl lg:text-6xl font-display font-bold mb-6">
                  About Versa Tradez
                </h1>
                <p className="text-xl text-primary-100 mb-8">
                  Egypt's premier courier and shipping company, connecting businesses and individuals 
                  across the nation with reliable, fast, and secure delivery solutions.
                </p>
                <div className="flex items-center text-primary-100">
                  <LocationIcon size={24} className="mr-3 text-accent-300" />
                  <span className="text-lg">Proudly serving Egypt since 2019</span>
                </div>
              </div>
              <div className="text-center">
                <PyramidIcon size={200} className="text-accent-300 mx-auto" />
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <Card key={index} className="text-center">
                  <CardContent className="p-6">
                    <div className="text-4xl mb-4">{stat.icon}</div>
                    <div className="text-3xl font-bold text-primary-600 mb-2">{stat.number}</div>
                    <div className="text-gray-600">{stat.label}</div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Our Story */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-3xl font-display font-bold text-gray-900 mb-6">
                  Our Story
                </h2>
                <div className="space-y-4 text-gray-600">
                  <p>
                    Founded in 2019 in the heart of Cairo, Versa Tradez was born from a simple vision: 
                    to revolutionize shipping and courier services across Egypt. Our founders recognized 
                    the need for a reliable, technology-driven logistics company that truly understands 
                    the Egyptian market.
                  </p>
                  <p>
                    Starting with just a small fleet of vehicles and a dedicated team, we've grown to 
                    become one of Egypt's most trusted shipping partners. From the bustling streets of 
                    Cairo to the historic ports of Alexandria, we've built a network that spans the entire country.
                  </p>
                  <p>
                    Today, we're proud to serve thousands of customers daily, from small businesses 
                    shipping their first products to large enterprises managing complex logistics operations. 
                    Our commitment to excellence and innovation continues to drive us forward.
                  </p>
                </div>
              </div>
              <div className="bg-gradient-to-br from-primary-100 to-accent-100 rounded-2xl p-8">
                <TruckIcon size={120} className="text-primary-600 mx-auto mb-6" />
                <div className="text-center">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    From Cairo to Every Corner of Egypt
                  </h3>
                  <p className="text-gray-600">
                    Our journey began with a single route and has expanded to cover all 27 governorates of Egypt.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Our Values */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-display font-bold text-gray-900 mb-4">
                Our Values
              </h2>
              <p className="text-xl text-gray-600">
                The principles that guide everything we do
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {values.map((value, index) => (
                <Card key={index} hover className="text-center h-full">
                  <CardContent className="p-6">
                    <div className="text-4xl mb-4">{value.icon}</div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-3">
                      {value.title}
                    </h3>
                    <p className="text-gray-600">
                      {value.description}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Team Section */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-display font-bold text-gray-900 mb-4">
                Meet Our Team
              </h2>
              <p className="text-xl text-gray-600">
                The dedicated professionals behind Versa Tradez
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {team.map((member, index) => (
                <Card key={index} hover className="text-center">
                  <CardContent className="p-6">
                    <div className="text-6xl mb-4">{member.image}</div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-1">
                      {member.name}
                    </h3>
                    <p className="text-primary-600 font-medium mb-3">
                      {member.role}
                    </p>
                    <p className="text-gray-600 text-sm">
                      {member.description}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Mission Section */}
        <section className="py-20 bg-gradient-to-r from-primary-600 to-accent-600">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl font-bold text-white mb-6">
              Our Mission
            </h2>
            <p className="text-xl text-primary-100 mb-8">
              To connect every corner of Egypt through reliable, innovative, and customer-focused 
              shipping solutions that empower businesses and individuals to thrive in the digital economy.
            </p>
            <div className="bg-white/10 rounded-2xl p-8 backdrop-blur-sm">
              <h3 className="text-2xl font-semibold text-white mb-4">
                Looking Ahead
              </h3>
              <p className="text-primary-100">
                As we continue to grow, we're expanding our services, investing in new technologies, 
                and building partnerships that will make shipping even more accessible and efficient 
                for all Egyptians.
              </p>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
}
