import React from 'react';

interface LogoProps {
  className?: string;
  width?: number;
  height?: number;
}

export const Logo: React.FC<LogoProps> = ({ 
  className = "", 
  width = 200, 
  height = 60 
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 200 60"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      {/* Background with Egyptian-inspired gradient */}
      <defs>
        <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stopColor="#2563eb" />
          <stop offset="50%" stopColor="#3b82f6" />
          <stop offset="100%" stopColor="#60a5fa" />
        </linearGradient>
        <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stopColor="#f97316" />
          <stop offset="100%" stopColor="#fb923c" />
        </linearGradient>
      </defs>
      
      {/* Main logo shape - stylized shipping container */}
      <rect x="10" y="15" width="40" height="30" rx="4" fill="url(#logoGradient)" />
      <rect x="15" y="20" width="30" height="4" fill="white" opacity="0.3" />
      <rect x="15" y="28" width="30" height="4" fill="white" opacity="0.3" />
      <rect x="15" y="36" width="30" height="4" fill="white" opacity="0.3" />
      
      {/* Egyptian pyramid accent */}
      <path d="M55 45 L65 25 L75 45 Z" fill="url(#accentGradient)" />
      <path d="M60 45 L65 35 L70 45 Z" fill="white" opacity="0.2" />
      
      {/* Company name */}
      <text x="85" y="25" fontFamily="Poppins, sans-serif" fontSize="18" fontWeight="700" fill="#1e40af">
        VERSA
      </text>
      <text x="85" y="42" fontFamily="Poppins, sans-serif" fontSize="14" fontWeight="500" fill="#f97316">
        TRADEZ
      </text>
      
      {/* Shipping lines accent */}
      <path d="M160 20 Q170 15 180 20" stroke="#60a5fa" strokeWidth="2" fill="none" />
      <path d="M160 30 Q170 25 180 30" stroke="#60a5fa" strokeWidth="2" fill="none" />
      <path d="M160 40 Q170 35 180 40" stroke="#60a5fa" strokeWidth="2" fill="none" />
    </svg>
  );
};
