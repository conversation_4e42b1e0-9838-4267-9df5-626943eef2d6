(()=>{var e={};e.id=220,e.ids=[220],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1172:()=>{},2612:()=>{},2854:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=t(9041),n=t(4374),i=t(6368),o=t.n(i),a=t(9543),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);t.d(r,l);let c={children:["",{children:["about",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,3862)),"C:\\Users\\<USER>\\Documents\\augment-projects\\vera tradez\\src\\app\\about\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,6259)),"C:\\Users\\<USER>\\Documents\\augment-projects\\vera tradez\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,524,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,3261,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,202,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Documents\\augment-projects\\vera tradez\\src\\app\\about\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/about/page",pathname:"/about",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3862:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var s=t(6551);function n(){return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)(Object(function(){var e=Error("Cannot find module '@/components/layout/Header'");throw e.code="MODULE_NOT_FOUND",e}()),{}),(0,s.jsxs)("main",{children:[(0,s.jsx)("section",{className:"bg-gradient-to-br from-primary-600 to-primary-800 text-white py-20",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-4xl lg:text-6xl font-display font-bold mb-6",children:"About Versa Tradez"}),(0,s.jsx)("p",{className:"text-xl text-primary-100 mb-8",children:"Egypt's premier courier and shipping company, connecting businesses and individuals across the nation with reliable, fast, and secure delivery solutions."}),(0,s.jsxs)("div",{className:"flex items-center text-primary-100",children:[(0,s.jsx)(Object(function(){var e=Error("Cannot find module '@/components/icons/ShippingIcons'");throw e.code="MODULE_NOT_FOUND",e}()),{size:24,className:"mr-3 text-accent-300"}),(0,s.jsx)("span",{className:"text-lg",children:"Proudly serving Egypt since 2019"})]})]}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)(Object(function(){var e=Error("Cannot find module '@/components/icons/ShippingIcons'");throw e.code="MODULE_NOT_FOUND",e}()),{size:200,className:"text-accent-300 mx-auto"})})]})})}),(0,s.jsx)("section",{className:"py-20 bg-white",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:[{number:"10,000+",label:"Packages Delivered",icon:"\uD83D\uDCE6"},{number:"50+",label:"Cities Covered",icon:"\uD83C\uDFD9️"},{number:"99%",label:"On-Time Delivery",icon:"⏰"},{number:"5+",label:"Years of Excellence",icon:"\uD83C\uDFC6"}].map((e,r)=>(0,s.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/Card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-center",children:(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/Card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"p-6",children:[(0,s.jsx)("div",{className:"text-4xl mb-4",children:e.icon}),(0,s.jsx)("div",{className:"text-3xl font-bold text-primary-600 mb-2",children:e.number}),(0,s.jsx)("div",{className:"text-gray-600",children:e.label})]})},r))})})}),(0,s.jsx)("section",{className:"py-20",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-3xl font-display font-bold text-gray-900 mb-6",children:"Our Story"}),(0,s.jsxs)("div",{className:"space-y-4 text-gray-600",children:[(0,s.jsx)("p",{children:"Founded in 2019 in the heart of Cairo, Versa Tradez was born from a simple vision: to revolutionize shipping and courier services across Egypt. Our founders recognized the need for a reliable, technology-driven logistics company that truly understands the Egyptian market."}),(0,s.jsx)("p",{children:"Starting with just a small fleet of vehicles and a dedicated team, we've grown to become one of Egypt's most trusted shipping partners. From the bustling streets of Cairo to the historic ports of Alexandria, we've built a network that spans the entire country."}),(0,s.jsx)("p",{children:"Today, we're proud to serve thousands of customers daily, from small businesses shipping their first products to large enterprises managing complex logistics operations. Our commitment to excellence and innovation continues to drive us forward."})]})]}),(0,s.jsxs)("div",{className:"bg-gradient-to-br from-primary-100 to-accent-100 rounded-2xl p-8",children:[(0,s.jsx)(Object(function(){var e=Error("Cannot find module '@/components/icons/ShippingIcons'");throw e.code="MODULE_NOT_FOUND",e}()),{size:120,className:"text-primary-600 mx-auto mb-6"}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"From Cairo to Every Corner of Egypt"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Our journey began with a single route and has expanded to cover all 27 governorates of Egypt."})]})]})]})})}),(0,s.jsx)("section",{className:"py-20 bg-white",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"text-center mb-16",children:[(0,s.jsx)("h2",{className:"text-3xl font-display font-bold text-gray-900 mb-4",children:"Our Values"}),(0,s.jsx)("p",{className:"text-xl text-gray-600",children:"The principles that guide everything we do"})]}),(0,s.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:[{title:"Reliability",description:"We deliver on our promises, ensuring your packages reach their destination safely and on time.",icon:"\uD83D\uDEE1️"},{title:"Innovation",description:"Leveraging cutting-edge technology to provide the best shipping experience in Egypt.",icon:"\uD83D\uDCA1"},{title:"Local Expertise",description:"Deep understanding of Egyptian markets, culture, and logistics challenges.",icon:"\uD83C\uDDEA\uD83C\uDDEC"},{title:"Customer First",description:"Every decision we make is centered around providing exceptional customer service.",icon:"❤️"}].map((e,r)=>(0,s.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/Card'");throw e.code="MODULE_NOT_FOUND",e}()),{hover:!0,className:"text-center h-full",children:(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/Card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"p-6",children:[(0,s.jsx)("div",{className:"text-4xl mb-4",children:e.icon}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:e.title}),(0,s.jsx)("p",{className:"text-gray-600",children:e.description})]})},r))})]})}),(0,s.jsx)("section",{className:"py-20",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"text-center mb-16",children:[(0,s.jsx)("h2",{className:"text-3xl font-display font-bold text-gray-900 mb-4",children:"Meet Our Team"}),(0,s.jsx)("p",{className:"text-xl text-gray-600",children:"The dedicated professionals behind Versa Tradez"})]}),(0,s.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:[{name:"Ahmed Hassan",role:"Founder & CEO",description:"Logistics expert with 15+ years in Egyptian shipping industry",image:"\uD83D\uDC68‍\uD83D\uDCBC"},{name:"Fatma El-Zahra",role:"Operations Director",description:"Ensures smooth operations across all Egyptian governorates",image:"\uD83D\uDC69‍\uD83D\uDCBC"},{name:"Omar Mahmoud",role:"Technology Lead",description:"Drives innovation in tracking and logistics technology",image:"\uD83D\uDC68‍\uD83D\uDCBB"},{name:"Nour Abdel-Rahman",role:"Customer Success Manager",description:"Dedicated to providing exceptional customer experiences",image:"\uD83D\uDC69‍\uD83C\uDF93"}].map((e,r)=>(0,s.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/Card'");throw e.code="MODULE_NOT_FOUND",e}()),{hover:!0,className:"text-center",children:(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/Card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"p-6",children:[(0,s.jsx)("div",{className:"text-6xl mb-4",children:e.image}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-1",children:e.name}),(0,s.jsx)("p",{className:"text-primary-600 font-medium mb-3",children:e.role}),(0,s.jsx)("p",{className:"text-gray-600 text-sm",children:e.description})]})},r))})]})}),(0,s.jsx)("section",{className:"py-20 bg-gradient-to-r from-primary-600 to-accent-600",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-white mb-6",children:"Our Mission"}),(0,s.jsx)("p",{className:"text-xl text-primary-100 mb-8",children:"To connect every corner of Egypt through reliable, innovative, and customer-focused shipping solutions that empower businesses and individuals to thrive in the digital economy."}),(0,s.jsxs)("div",{className:"bg-white/10 rounded-2xl p-8 backdrop-blur-sm",children:[(0,s.jsx)("h3",{className:"text-2xl font-semibold text-white mb-4",children:"Looking Ahead"}),(0,s.jsx)("p",{className:"text-primary-100",children:"As we continue to grow, we're expanding our services, investing in new technologies, and building partnerships that will make shipping even more accessible and efficient for all Egyptians."})]})]})})]}),(0,s.jsx)(Object(function(){var e=Error("Cannot find module '@/components/layout/Footer'");throw e.code="MODULE_NOT_FOUND",e}()),{})]})}t(6770),!function(){var e=Error("Cannot find module '@/components/layout/Header'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/Footer'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/Card'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/icons/ShippingIcons'");throw e.code="MODULE_NOT_FOUND",e}()},3873:e=>{"use strict";e.exports=require("path")},5521:()=>{},5793:()=>{},6259:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c,metadata:()=>l});var s=t(6551),n=t(1),i=t.n(n),o=t(6138),a=t.n(o);t(6393);let l={title:"Versa Tradez - Egyptian Shipment Courier & Tracking",description:"Professional courier and shipment tracking services across Egypt. Fast, reliable, and secure delivery solutions for businesses and individuals.",keywords:"courier, shipping, tracking, Egypt, delivery, logistics, freight"};function c({children:e}){return(0,s.jsx)("html",{lang:"en",className:`${i().variable} ${a().variable}`,children:(0,s.jsx)("body",{className:"font-sans antialiased",children:e})})}},6393:()=>{},8371:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,7562,23)),Promise.resolve().then(t.t.bind(t,9100,23)),Promise.resolve().then(t.t.bind(t,6368,23)),Promise.resolve().then(t.t.bind(t,9907,23)),Promise.resolve().then(t.t.bind(t,9315,23)),Promise.resolve().then(t.t.bind(t,6715,23)),Promise.resolve().then(t.t.bind(t,7403,23)),Promise.resolve().then(t.t.bind(t,5133,23))},8619:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,1892,23)),Promise.resolve().then(t.t.bind(t,138,23)),Promise.resolve().then(t.t.bind(t,218,23)),Promise.resolve().then(t.t.bind(t,4349,23)),Promise.resolve().then(t.t.bind(t,2809,23)),Promise.resolve().then(t.t.bind(t,7569,23)),Promise.resolve().then(t.t.bind(t,4185,23)),Promise.resolve().then(t.t.bind(t,3243,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[252],()=>t(2854));module.exports=s})();