(()=>{var e={};e.id=970,e.ids=[970],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1172:()=>{},2612:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3954:(e,r,t)=>{Promise.resolve().then(t.bind(t,8342))},6259:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d,metadata:()=>l});var s=t(6551),n=t(1),o=t.n(n),a=t(6138),i=t.n(a);t(6393);let l={title:"Versa Tradez - Egyptian Shipment Courier & Tracking",description:"Professional courier and shipment tracking services across Egypt. Fast, reliable, and secure delivery solutions for businesses and individuals.",keywords:"courier, shipping, tracking, Egypt, delivery, logistics, freight"};function d({children:e}){return(0,s.jsx)("html",{lang:"en",className:`${o().variable} ${i().variable}`,children:(0,s.jsx)("body",{className:"font-sans antialiased",children:e})})}},6393:()=>{},7664:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(5657).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\vera tradez\\\\src\\\\app\\\\track\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\vera tradez\\src\\app\\track\\page.tsx","default")},8342:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var s=t(5757),n=t(7992);function o(){let[e,r]=(0,n.useState)(null);return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)(Object(function(){var e=Error("Cannot find module '@/components/layout/Header'");throw e.code="MODULE_NOT_FOUND",e}()),{}),(0,s.jsx)("main",{className:"py-12",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsx)("h1",{className:"text-4xl font-display font-bold text-gray-900 mb-4",children:"Track Your Shipment"}),(0,s.jsx)("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Get real-time updates on your package delivery status and location across Egypt."})]}),e?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("button",{onClick:()=>{r(null)},className:"text-primary-600 hover:text-primary-700 font-medium",children:"← Track Another Package"})}),(0,s.jsx)(Object(function(){var e=Error("Cannot find module '@/components/tracking/TrackingResult'");throw e.code="MODULE_NOT_FOUND",e}()),{trackingId:e})]}):(0,s.jsx)(Object(function(){var e=Error("Cannot find module '@/components/tracking/TrackingForm'");throw e.code="MODULE_NOT_FOUND",e}()),{onTrack:e=>{r(e)}}),(0,s.jsxs)("div",{className:"mt-16 bg-white rounded-xl shadow-lg p-8",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Need Help?"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Can't find your tracking information or have questions about your shipment?"})]}),(0,s.jsxs)("div",{className:"grid md:grid-cols-3 gap-6",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})})}),(0,s.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Call Us"}),(0,s.jsx)("p",{className:"text-gray-600 text-sm",children:"+20 2 1234 5678"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),(0,s.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Email Us"}),(0,s.jsx)("p",{className:"text-gray-600 text-sm",children:"<EMAIL>"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}),(0,s.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Live Chat"}),(0,s.jsx)("p",{className:"text-gray-600 text-sm",children:"Available 24/7"})]})]})]})]})}),(0,s.jsx)(Object(function(){var e=Error("Cannot find module '@/components/layout/Footer'");throw e.code="MODULE_NOT_FOUND",e}()),{})]})}!function(){var e=Error("Cannot find module '@/components/layout/Header'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/Footer'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/tracking/TrackingForm'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/tracking/TrackingResult'");throw e.code="MODULE_NOT_FOUND",e}()},8371:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,7562,23)),Promise.resolve().then(t.t.bind(t,9100,23)),Promise.resolve().then(t.t.bind(t,6368,23)),Promise.resolve().then(t.t.bind(t,9907,23)),Promise.resolve().then(t.t.bind(t,9315,23)),Promise.resolve().then(t.t.bind(t,6715,23)),Promise.resolve().then(t.t.bind(t,7403,23)),Promise.resolve().then(t.t.bind(t,5133,23))},8619:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,1892,23)),Promise.resolve().then(t.t.bind(t,138,23)),Promise.resolve().then(t.t.bind(t,218,23)),Promise.resolve().then(t.t.bind(t,4349,23)),Promise.resolve().then(t.t.bind(t,2809,23)),Promise.resolve().then(t.t.bind(t,7569,23)),Promise.resolve().then(t.t.bind(t,4185,23)),Promise.resolve().then(t.t.bind(t,3243,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9674:(e,r,t)=>{Promise.resolve().then(t.bind(t,7664))},9918:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=t(9041),n=t(4374),o=t(6368),a=t.n(o),i=t(9543),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let d={children:["",{children:["track",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,7664)),"C:\\Users\\<USER>\\Documents\\augment-projects\\vera tradez\\src\\app\\track\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,6259)),"C:\\Users\\<USER>\\Documents\\augment-projects\\vera tradez\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,524,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,3261,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,202,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\vera tradez\\src\\app\\track\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/track/page",pathname:"/track",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[252],()=>t(9918));module.exports=s})();