import React from 'react';
import { Head<PERSON> } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';
import { ContactForm } from '@/components/forms/ContactForm';
import { Card, CardContent } from '@/components/ui/Card';
import { LocationIcon, PyramidIcon } from '@/components/icons/ShippingIcons';

export default function ContactPage() {
  const offices = [
    {
      city: 'Cairo',
      address: '123 Tahrir Square, Downtown Cairo, Egypt',
      phone: '+20 2 1234 5678',
      email: '<EMAIL>',
      hours: 'Sun-Thu: 9:00 AM - 6:00 PM',
      isMain: true
    },
    {
      city: 'Alexandria',
      address: '456 Corniche Road, Alexandria, Egypt',
      phone: '+20 3 1234 5678',
      email: '<EMAIL>',
      hours: 'Sun-Thu: 9:00 AM - 6:00 PM',
      isMain: false
    },
    {
      city: 'Giza',
      address: '789 Pyramid Street, Giza, Egypt',
      phone: '+20 2 9876 5432',
      email: '<EMAIL>',
      hours: 'Sun-Thu: 9:00 AM - 6:00 PM',
      isMain: false
    }
  ];

  const contactMethods = [
    {
      title: 'Phone Support',
      description: 'Speak directly with our customer service team',
      contact: '+20 2 1234 5678',
      hours: '24/7 Support Available',
      icon: '📞'
    },
    {
      title: 'Email Support',
      description: 'Send us an email and we\'ll respond within 24 hours',
      contact: '<EMAIL>',
      hours: 'Response within 24 hours',
      icon: '📧'
    },
    {
      title: 'Live Chat',
      description: 'Chat with our support team in real-time',
      contact: 'Available on website',
      hours: 'Sun-Thu: 9:00 AM - 6:00 PM',
      icon: '💬'
    },
    {
      title: 'WhatsApp',
      description: 'Message us on WhatsApp for quick support',
      contact: '+20 10 1234 5678',
      hours: '24/7 Available',
      icon: '📱'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main>
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-primary-600 to-primary-800 text-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div className="flex justify-center mb-6">
              <LocationIcon size={80} className="text-accent-300" />
            </div>
            <h1 className="text-4xl lg:text-6xl font-display font-bold mb-6">
              Contact Us
            </h1>
            <p className="text-xl text-primary-100 max-w-3xl mx-auto">
              Get in touch with Egypt's most reliable shipping partner. We're here to help with all your logistics needs.
            </p>
          </div>
        </section>

        {/* Contact Methods */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-display font-bold text-gray-900 mb-4">
                How Can We Help?
              </h2>
              <p className="text-xl text-gray-600">
                Choose the best way to reach us
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
              {contactMethods.map((method, index) => (
                <Card key={index} hover className="text-center h-full">
                  <CardContent className="p-6">
                    <div className="text-4xl mb-4">{method.icon}</div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {method.title}
                    </h3>
                    <p className="text-gray-600 text-sm mb-4">
                      {method.description}
                    </p>
                    <div className="space-y-1">
                      <p className="font-medium text-primary-600">{method.contact}</p>
                      <p className="text-xs text-gray-500">{method.hours}</p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Contact Form and Map */}
            <div className="grid lg:grid-cols-2 gap-12">
              {/* Contact Form */}
              <div>
                <ContactForm />
              </div>

              {/* Office Locations */}
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Our Offices</h2>
                  <p className="text-gray-600 mb-8">
                    Visit us at any of our locations across Egypt for in-person assistance.
                  </p>
                </div>

                {offices.map((office, index) => (
                  <Card key={index} className={office.isMain ? 'border-primary-200 bg-primary-50' : ''}>
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between mb-4">
                        <h3 className="text-xl font-semibold text-gray-900 flex items-center">
                          {office.city}
                          {office.isMain && (
                            <span className="ml-2 px-2 py-1 bg-primary-600 text-white text-xs rounded-full">
                              Main Office
                            </span>
                          )}
                        </h3>
                        <PyramidIcon size={24} className="text-accent-500" />
                      </div>
                      
                      <div className="space-y-3">
                        <div className="flex items-start">
                          <LocationIcon size={16} className="mr-3 mt-1 text-gray-400 flex-shrink-0" />
                          <span className="text-gray-600">{office.address}</span>
                        </div>
                        
                        <div className="flex items-center">
                          <span className="mr-3 text-gray-400">📞</span>
                          <span className="text-gray-600">{office.phone}</span>
                        </div>
                        
                        <div className="flex items-center">
                          <span className="mr-3 text-gray-400">📧</span>
                          <span className="text-gray-600">{office.email}</span>
                        </div>
                        
                        <div className="flex items-center">
                          <span className="mr-3 text-gray-400">🕒</span>
                          <span className="text-gray-600">{office.hours}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}

                {/* Map Placeholder */}
                <Card>
                  <CardContent className="p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Find Us on Map</h3>
                    <div className="bg-gray-200 rounded-lg h-64 flex items-center justify-center">
                      <div className="text-center text-gray-500">
                        <LocationIcon size={48} className="mx-auto mb-2" />
                        <p>Interactive map would be integrated here</p>
                        <p className="text-sm">Showing all Versa Tradez locations across Egypt</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-20 bg-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-display font-bold text-gray-900 mb-4">
                Frequently Asked Questions
              </h2>
              <p className="text-xl text-gray-600">
                Quick answers to common questions
              </p>
            </div>

            <div className="space-y-6">
              {[
                {
                  question: 'What are your delivery times?',
                  answer: 'We offer same-day delivery in major cities and 2-5 business days for standard shipping across Egypt.'
                },
                {
                  question: 'How can I track my package?',
                  answer: 'Use our online tracking system with your tracking ID, or contact our customer service for updates.'
                },
                {
                  question: 'Do you offer international shipping?',
                  answer: 'Yes, we provide international express shipping to over 200 countries worldwide.'
                },
                {
                  question: 'What payment methods do you accept?',
                  answer: 'We accept cash, credit cards, bank transfers, and offer cash-on-delivery services.'
                }
              ].map((faq, index) => (
                <Card key={index}>
                  <CardContent className="p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {faq.question}
                    </h3>
                    <p className="text-gray-600">
                      {faq.answer}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
}
