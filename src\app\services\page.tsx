import React from 'react';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { TruckIcon, PackageIcon, ShipIcon, PlaneIcon, PyramidIcon } from '@/components/icons/ShippingIcons';

export default function ServicesPage() {
  const services = [
    {
      icon: TruckIcon,
      title: 'Express Delivery',
      description: 'Lightning-fast same-day and next-day delivery across major Egyptian cities.',
      features: [
        'Same-day delivery in Cairo, Alexandria, and Giza',
        'Next-day delivery to 50+ cities across Egypt',
        'Real-time GPS tracking',
        'Secure handling and insurance',
        'Proof of delivery with signature',
        'Temperature-controlled options'
      ],
      pricing: 'Starting from 50 EGP',
      deliveryTime: '4-24 hours'
    },
    {
      icon: PackageIcon,
      title: 'Standard Shipping',
      description: 'Reliable and cost-effective shipping solutions for everyday needs.',
      features: [
        '2-5 business days delivery',
        'Full insurance coverage included',
        'Bulk shipping discounts',
        'Flexible pickup scheduling',
        'SMS and email notifications',
        'Free packaging materials'
      ],
      pricing: 'Starting from 25 EGP',
      deliveryTime: '2-5 business days'
    },
    {
      icon: ShipIcon,
      title: 'Freight Services',
      description: 'Heavy cargo and bulk shipments via sea and land routes.',
      features: [
        'Heavy cargo up to 50 tons',
        'Port-to-port shipping',
        'Custom clearance assistance',
        'Warehousing solutions',
        'Container shipping',
        'Specialized equipment handling'
      ],
      pricing: 'Custom quotes',
      deliveryTime: '5-15 business days'
    },
    {
      icon: PlaneIcon,
      title: 'International Express',
      description: 'Fast international shipping connecting Egypt with the world.',
      features: [
        'Global network coverage',
        'Express customs clearance',
        'Door-to-door service',
        'Document and parcel shipping',
        'Duty and tax calculation',
        'International tracking'
      ],
      pricing: 'Starting from 200 EGP',
      deliveryTime: '1-7 business days'
    }
  ];

  const additionalServices = [
    {
      title: 'Packaging Services',
      description: 'Professional packaging for fragile and valuable items',
      icon: '📦'
    },
    {
      title: 'Warehousing',
      description: 'Secure storage solutions with inventory management',
      icon: '🏢'
    },
    {
      title: 'Cash on Delivery',
      description: 'COD services for e-commerce businesses',
      icon: '💰'
    },
    {
      title: 'Return Management',
      description: 'Hassle-free return and exchange services',
      icon: '🔄'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main>
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-primary-600 to-primary-800 text-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div className="flex justify-center mb-6">
              <PyramidIcon size={80} className="text-accent-300" />
            </div>
            <h1 className="text-4xl lg:text-6xl font-display font-bold mb-6">
              Our Services
            </h1>
            <p className="text-xl text-primary-100 max-w-3xl mx-auto">
              Comprehensive shipping and logistics solutions designed to meet all your delivery needs across Egypt and beyond.
            </p>
          </div>
        </section>

        {/* Main Services */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-8">
              {services.map((service, index) => (
                <Card key={index} className="h-full">
                  <CardHeader>
                    <div className="flex items-center mb-4">
                      <div className="w-12 h-12 bg-primary-100 text-primary-600 rounded-lg flex items-center justify-center mr-4">
                        <service.icon size={24} />
                      </div>
                      <div>
                        <h3 className="text-2xl font-bold text-gray-900">{service.title}</h3>
                        <p className="text-gray-600">{service.description}</p>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid md:grid-cols-2 gap-6 mb-6">
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-3">Features:</h4>
                        <ul className="space-y-2">
                          {service.features.map((feature, featureIndex) => (
                            <li key={featureIndex} className="flex items-start text-sm text-gray-600">
                              <svg className="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </div>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-1">Pricing:</h4>
                          <p className="text-primary-600 font-medium">{service.pricing}</p>
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-1">Delivery Time:</h4>
                          <p className="text-gray-600">{service.deliveryTime}</p>
                        </div>
                        <Button variant="primary" className="w-full">
                          Get Quote
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Additional Services */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-display font-bold text-gray-900 mb-4">
                Additional Services
              </h2>
              <p className="text-xl text-gray-600">
                Comprehensive solutions to support your shipping needs
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {additionalServices.map((service, index) => (
                <Card key={index} hover className="text-center">
                  <CardContent className="p-6">
                    <div className="text-4xl mb-4">{service.icon}</div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {service.title}
                    </h3>
                    <p className="text-gray-600 text-sm">
                      {service.description}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-primary-600 to-accent-600">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl font-bold text-white mb-4">
              Ready to Ship with Versa Tradez?
            </h2>
            <p className="text-xl text-primary-100 mb-8">
              Get started today and experience Egypt's most reliable shipping service.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="secondary" size="lg">
                Get Free Quote
              </Button>
              <Button variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-primary-600">
                Contact Sales
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
}
