'use client';

import React from 'react';
import { Button } from '@/components/ui/Button';
import { TruckIcon, PyramidIcon } from '@/components/icons/ShippingIcons';

export const HeroSection: React.FC = () => {
  return (
    <section className="relative bg-gradient-to-br from-primary-50 via-white to-accent-50 overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 text-primary-100">
          <PyramidIcon size={120} />
        </div>
        <div className="absolute bottom-20 right-10 text-accent-100">
          <TruckIcon size={100} />
        </div>
        <div className="absolute top-1/2 left-1/4 w-32 h-32 bg-primary-100 rounded-full opacity-20"></div>
        <div className="absolute top-1/3 right-1/4 w-24 h-24 bg-accent-100 rounded-full opacity-20"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="text-center lg:text-left">
            <h1 className="text-4xl lg:text-6xl font-display font-bold text-gray-900 mb-6">
              Fast & Reliable
              <span className="block text-primary-600">Shipping Solutions</span>
              <span className="block text-accent-600">Across Egypt</span>
            </h1>
            
            <p className="text-xl text-gray-600 mb-8 max-w-2xl">
              Versa Tradez provides professional courier and tracking services throughout Egypt. 
              From Cairo to Alexandria, we ensure your packages reach their destination safely and on time.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Button variant="primary" size="lg" className="text-lg px-8 py-4">
                Ship Now
              </Button>
              <Button variant="outline" size="lg" className="text-lg px-8 py-4">
                Track Package
              </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-8 mt-12 pt-8 border-t border-gray-200">
              <div className="text-center lg:text-left">
                <div className="text-3xl font-bold text-primary-600">10K+</div>
                <div className="text-sm text-gray-600">Packages Delivered</div>
              </div>
              <div className="text-center lg:text-left">
                <div className="text-3xl font-bold text-primary-600">50+</div>
                <div className="text-sm text-gray-600">Cities Covered</div>
              </div>
              <div className="text-center lg:text-left">
                <div className="text-3xl font-bold text-primary-600">99%</div>
                <div className="text-sm text-gray-600">On-Time Delivery</div>
              </div>
            </div>
          </div>

          {/* Hero Image/Illustration */}
          <div className="relative">
            <div className="bg-gradient-to-br from-primary-500 to-primary-700 rounded-3xl p-8 shadow-2xl">
              <div className="bg-white rounded-2xl p-6">
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="text-sm font-medium">Package Picked Up</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span className="text-sm font-medium">In Transit - Cairo</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <span className="text-sm font-medium">Out for Delivery</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
                    <span className="text-sm text-gray-500">Delivered</span>
                  </div>
                </div>
                <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                  <div className="text-xs text-gray-500 mb-1">Tracking ID</div>
                  <div className="font-mono text-sm font-medium">VT-2024-EG-001234</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
