(()=>{var e={};e.id=974,e.ids=[974],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1172:()=>{},2612:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5521:()=>{},5793:()=>{},6259:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c,metadata:()=>d});var n=t(6551),o=t(1),s=t.n(o),i=t(6138),a=t.n(i);t(6393);let d={title:"Versa Tradez - Egyptian Shipment Courier & Tracking",description:"Professional courier and shipment tracking services across Egypt. Fast, reliable, and secure delivery solutions for businesses and individuals.",keywords:"courier, shipping, tracking, Egypt, delivery, logistics, freight"};function c({children:e}){return(0,n.jsx)("html",{lang:"en",className:`${s().variable} ${a().variable}`,children:(0,n.jsx)("body",{className:"font-sans antialiased",children:e})})}},6393:()=>{},8371:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,7562,23)),Promise.resolve().then(t.t.bind(t,9100,23)),Promise.resolve().then(t.t.bind(t,6368,23)),Promise.resolve().then(t.t.bind(t,9907,23)),Promise.resolve().then(t.t.bind(t,9315,23)),Promise.resolve().then(t.t.bind(t,6715,23)),Promise.resolve().then(t.t.bind(t,7403,23)),Promise.resolve().then(t.t.bind(t,5133,23))},8619:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,1892,23)),Promise.resolve().then(t.t.bind(t,138,23)),Promise.resolve().then(t.t.bind(t,218,23)),Promise.resolve().then(t.t.bind(t,4349,23)),Promise.resolve().then(t.t.bind(t,2809,23)),Promise.resolve().then(t.t.bind(t,7569,23)),Promise.resolve().then(t.t.bind(t,4185,23)),Promise.resolve().then(t.t.bind(t,3243,23))},8744:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var n=t(6551);function o(){return(0,n.jsxs)("div",{className:"min-h-screen",children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/layout/Header'");throw e.code="MODULE_NOT_FOUND",e}()),{}),(0,n.jsxs)("main",{children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/sections/HeroSection'");throw e.code="MODULE_NOT_FOUND",e}()),{}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/sections/ServicesSection'");throw e.code="MODULE_NOT_FOUND",e}()),{})]}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/layout/Footer'");throw e.code="MODULE_NOT_FOUND",e}()),{})]})}t(6770),!function(){var e=Error("Cannot find module '@/components/layout/Header'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/Footer'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/sections/HeroSection'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/sections/ServicesSection'");throw e.code="MODULE_NOT_FOUND",e}()},8766:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>p,tree:()=>c});var n=t(9041),o=t(4374),s=t(6368),i=t.n(s),a=t(9543),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);t.d(r,d);let c=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,8744)),"C:\\Users\\<USER>\\Documents\\augment-projects\\vera tradez\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,6259)),"C:\\Users\\<USER>\\Documents\\augment-projects\\vera tradez\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,524,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,3261,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,202,23)),"next/dist/client/components/unauthorized-error"]}],l=["C:\\Users\\<USER>\\Documents\\augment-projects\\vera tradez\\src\\app\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[252],()=>t(8766));module.exports=n})();