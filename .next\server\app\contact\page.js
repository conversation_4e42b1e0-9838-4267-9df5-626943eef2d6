(()=>{var e={};e.id=977,e.ids=[977],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1172:()=>{},2612:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5521:()=>{},5793:()=>{},6091:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(6551);function n(){return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/layout/Header'");throw e.code="MODULE_NOT_FOUND",e}()),{}),(0,r.jsxs)("main",{children:[(0,r.jsx)("section",{className:"bg-gradient-to-br from-primary-600 to-primary-800 text-white py-20",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,r.jsx)("div",{className:"flex justify-center mb-6",children:(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/icons/ShippingIcons'");throw e.code="MODULE_NOT_FOUND",e}()),{size:80,className:"text-accent-300"})}),(0,r.jsx)("h1",{className:"text-4xl lg:text-6xl font-display font-bold mb-6",children:"Contact Us"}),(0,r.jsx)("p",{className:"text-xl text-primary-100 max-w-3xl mx-auto",children:"Get in touch with Egypt's most reliable shipping partner. We're here to help with all your logistics needs."})]})}),(0,r.jsx)("section",{className:"py-20",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"text-center mb-16",children:[(0,r.jsx)("h2",{className:"text-3xl font-display font-bold text-gray-900 mb-4",children:"How Can We Help?"}),(0,r.jsx)("p",{className:"text-xl text-gray-600",children:"Choose the best way to reach us"})]}),(0,r.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16",children:[{title:"Phone Support",description:"Speak directly with our customer service team",contact:"+20 2 1234 5678",hours:"24/7 Support Available",icon:"\uD83D\uDCDE"},{title:"Email Support",description:"Send us an email and we'll respond within 24 hours",contact:"<EMAIL>",hours:"Response within 24 hours",icon:"\uD83D\uDCE7"},{title:"Live Chat",description:"Chat with our support team in real-time",contact:"Available on website",hours:"Sun-Thu: 9:00 AM - 6:00 PM",icon:"\uD83D\uDCAC"},{title:"WhatsApp",description:"Message us on WhatsApp for quick support",contact:"+20 10 1234 5678",hours:"24/7 Available",icon:"\uD83D\uDCF1"}].map((e,s)=>(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/Card'");throw e.code="MODULE_NOT_FOUND",e}()),{hover:!0,className:"text-center h-full",children:(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/Card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"p-6",children:[(0,r.jsx)("div",{className:"text-4xl mb-4",children:e.icon}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:e.title}),(0,r.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:e.description}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("p",{className:"font-medium text-primary-600",children:e.contact}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:e.hours})]})]})},s))}),(0,r.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12",children:[(0,r.jsx)("div",{children:(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/forms/ContactForm'");throw e.code="MODULE_NOT_FOUND",e}()),{})}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Our Offices"}),(0,r.jsx)("p",{className:"text-gray-600 mb-8",children:"Visit us at any of our locations across Egypt for in-person assistance."})]}),[{city:"Cairo",address:"123 Tahrir Square, Downtown Cairo, Egypt",phone:"+20 2 1234 5678",email:"<EMAIL>",hours:"Sun-Thu: 9:00 AM - 6:00 PM",isMain:!0},{city:"Alexandria",address:"456 Corniche Road, Alexandria, Egypt",phone:"+20 3 1234 5678",email:"<EMAIL>",hours:"Sun-Thu: 9:00 AM - 6:00 PM",isMain:!1},{city:"Giza",address:"789 Pyramid Street, Giza, Egypt",phone:"+20 2 9876 5432",email:"<EMAIL>",hours:"Sun-Thu: 9:00 AM - 6:00 PM",isMain:!1}].map((e,s)=>(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/Card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:e.isMain?"border-primary-200 bg-primary-50":"",children:(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/Card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,r.jsxs)("h3",{className:"text-xl font-semibold text-gray-900 flex items-center",children:[e.city,e.isMain&&(0,r.jsx)("span",{className:"ml-2 px-2 py-1 bg-primary-600 text-white text-xs rounded-full",children:"Main Office"})]}),(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/icons/ShippingIcons'");throw e.code="MODULE_NOT_FOUND",e}()),{size:24,className:"text-accent-500"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/icons/ShippingIcons'");throw e.code="MODULE_NOT_FOUND",e}()),{size:16,className:"mr-3 mt-1 text-gray-400 flex-shrink-0"}),(0,r.jsx)("span",{className:"text-gray-600",children:e.address})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"mr-3 text-gray-400",children:"\uD83D\uDCDE"}),(0,r.jsx)("span",{className:"text-gray-600",children:e.phone})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"mr-3 text-gray-400",children:"\uD83D\uDCE7"}),(0,r.jsx)("span",{className:"text-gray-600",children:e.email})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"mr-3 text-gray-400",children:"\uD83D\uDD52"}),(0,r.jsx)("span",{className:"text-gray-600",children:e.hours})]})]})]})},s)),(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/Card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/Card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Find Us on Map"}),(0,r.jsx)("div",{className:"bg-gray-200 rounded-lg h-64 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center text-gray-500",children:[(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/icons/ShippingIcons'");throw e.code="MODULE_NOT_FOUND",e}()),{size:48,className:"mx-auto mb-2"}),(0,r.jsx)("p",{children:"Interactive map would be integrated here"}),(0,r.jsx)("p",{className:"text-sm",children:"Showing all Versa Tradez locations across Egypt"})]})})]})})]})]})]})}),(0,r.jsx)("section",{className:"py-20 bg-white",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"text-center mb-16",children:[(0,r.jsx)("h2",{className:"text-3xl font-display font-bold text-gray-900 mb-4",children:"Frequently Asked Questions"}),(0,r.jsx)("p",{className:"text-xl text-gray-600",children:"Quick answers to common questions"})]}),(0,r.jsx)("div",{className:"space-y-6",children:[{question:"What are your delivery times?",answer:"We offer same-day delivery in major cities and 2-5 business days for standard shipping across Egypt."},{question:"How can I track my package?",answer:"Use our online tracking system with your tracking ID, or contact our customer service for updates."},{question:"Do you offer international shipping?",answer:"Yes, we provide international express shipping to over 200 countries worldwide."},{question:"What payment methods do you accept?",answer:"We accept cash, credit cards, bank transfers, and offer cash-on-delivery services."}].map((e,s)=>(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/Card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/Card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:e.question}),(0,r.jsx)("p",{className:"text-gray-600",children:e.answer})]})},s))})]})})]}),(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/layout/Footer'");throw e.code="MODULE_NOT_FOUND",e}()),{})]})}t(6770),!function(){var e=Error("Cannot find module '@/components/layout/Header'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/Footer'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/forms/ContactForm'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/Card'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/icons/ShippingIcons'");throw e.code="MODULE_NOT_FOUND",e}()},6210:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>l,routeModule:()=>p,tree:()=>d});var r=t(9041),n=t(4374),a=t(6368),o=t.n(a),i=t(9543),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);t.d(s,c);let d={children:["",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,6091)),"C:\\Users\\<USER>\\Documents\\augment-projects\\vera tradez\\src\\app\\contact\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,6259)),"C:\\Users\\<USER>\\Documents\\augment-projects\\vera tradez\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,524,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,3261,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,202,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["C:\\Users\\<USER>\\Documents\\augment-projects\\vera tradez\\src\\app\\contact\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/contact/page",pathname:"/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},6259:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d,metadata:()=>c});var r=t(6551),n=t(1),a=t.n(n),o=t(6138),i=t.n(o);t(6393);let c={title:"Versa Tradez - Egyptian Shipment Courier & Tracking",description:"Professional courier and shipment tracking services across Egypt. Fast, reliable, and secure delivery solutions for businesses and individuals.",keywords:"courier, shipping, tracking, Egypt, delivery, logistics, freight"};function d({children:e}){return(0,r.jsx)("html",{lang:"en",className:`${a().variable} ${i().variable}`,children:(0,r.jsx)("body",{className:"font-sans antialiased",children:e})})}},6393:()=>{},8371:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,7562,23)),Promise.resolve().then(t.t.bind(t,9100,23)),Promise.resolve().then(t.t.bind(t,6368,23)),Promise.resolve().then(t.t.bind(t,9907,23)),Promise.resolve().then(t.t.bind(t,9315,23)),Promise.resolve().then(t.t.bind(t,6715,23)),Promise.resolve().then(t.t.bind(t,7403,23)),Promise.resolve().then(t.t.bind(t,5133,23))},8619:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,1892,23)),Promise.resolve().then(t.t.bind(t,138,23)),Promise.resolve().then(t.t.bind(t,218,23)),Promise.resolve().then(t.t.bind(t,4349,23)),Promise.resolve().then(t.t.bind(t,2809,23)),Promise.resolve().then(t.t.bind(t,7569,23)),Promise.resolve().then(t.t.bind(t,4185,23)),Promise.resolve().then(t.t.bind(t,3243,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[252],()=>t(6210));module.exports=r})();