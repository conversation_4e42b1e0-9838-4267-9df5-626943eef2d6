import React from 'react';
import Link from 'next/link';
import { Logo } from '@/components/icons/Logo';
import { LocationIcon, PyramidIcon } from '@/components/icons/ShippingIcons';

export const Footer: React.FC = () => {
  const footerLinks = {
    services: [
      { name: 'Express Delivery', href: '/services/express' },
      { name: 'Standard Shipping', href: '/services/standard' },
      { name: 'Freight Services', href: '/services/freight' },
      { name: 'International', href: '/services/international' },
    ],
    company: [
      { name: 'About Us', href: '/about' },
      { name: 'Careers', href: '/careers' },
      { name: 'News', href: '/news' },
      { name: 'Contact', href: '/contact' },
    ],
    support: [
      { name: 'Track Package', href: '/track' },
      { name: 'Help Center', href: '/help' },
      { name: 'Shipping Guide', href: '/guide' },
      { name: 'Terms of Service', href: '/terms' },
    ],
  };

  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-1">
            <Logo className="mb-6" />
            <p className="text-gray-300 mb-6">
              Egypt's trusted courier and shipping partner, delivering excellence across the nation with modern technology and traditional reliability.
            </p>
            <div className="flex items-center text-gray-300 mb-4">
              <LocationIcon size={20} className="mr-3 text-accent-500" />
              <span>Cairo, Egypt</span>
            </div>
            <div className="flex items-center text-gray-300">
              <PyramidIcon size={20} className="mr-3 text-accent-500" />
              <span>Proudly Egyptian</span>
            </div>
          </div>

          {/* Services */}
          <div>
            <h3 className="text-lg font-semibold mb-6">Services</h3>
            <ul className="space-y-3">
              {footerLinks.services.map((link) => (
                <li key={link.name}>
                  <Link href={link.href} className="text-gray-300 hover:text-white transition-colors">
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Company */}
          <div>
            <h3 className="text-lg font-semibold mb-6">Company</h3>
            <ul className="space-y-3">
              {footerLinks.company.map((link) => (
                <li key={link.name}>
                  <Link href={link.href} className="text-gray-300 hover:text-white transition-colors">
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Support */}
          <div>
            <h3 className="text-lg font-semibold mb-6">Support</h3>
            <ul className="space-y-3">
              {footerLinks.support.map((link) => (
                <li key={link.name}>
                  <Link href={link.href} className="text-gray-300 hover:text-white transition-colors">
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © 2024 Versa Tradez. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <Link href="/privacy" className="text-gray-400 hover:text-white text-sm transition-colors">
                Privacy Policy
              </Link>
              <Link href="/terms" className="text-gray-400 hover:text-white text-sm transition-colors">
                Terms of Service
              </Link>
              <Link href="/cookies" className="text-gray-400 hover:text-white text-sm transition-colors">
                Cookie Policy
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};
