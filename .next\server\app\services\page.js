(()=>{var e={};e.id=763,e.ids=[763],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1172:()=>{},2612:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5184:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>l});var n=t(9041),s=t(4374),i=t(6368),o=t.n(i),a=t(9543),c={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>a[e]);t.d(r,c);let l={children:["",{children:["services",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,6623)),"C:\\Users\\<USER>\\Documents\\augment-projects\\vera tradez\\src\\app\\services\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,6259)),"C:\\Users\\<USER>\\Documents\\augment-projects\\vera tradez\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,524,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,3261,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,202,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Documents\\augment-projects\\vera tradez\\src\\app\\services\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/services/page",pathname:"/services",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},5521:()=>{},5793:()=>{},6259:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l,metadata:()=>c});var n=t(6551),s=t(1),i=t.n(s),o=t(6138),a=t.n(o);t(6393);let c={title:"Versa Tradez - Egyptian Shipment Courier & Tracking",description:"Professional courier and shipment tracking services across Egypt. Fast, reliable, and secure delivery solutions for businesses and individuals.",keywords:"courier, shipping, tracking, Egypt, delivery, logistics, freight"};function l({children:e}){return(0,n.jsx)("html",{lang:"en",className:`${i().variable} ${a().variable}`,children:(0,n.jsx)("body",{className:"font-sans antialiased",children:e})})}},6393:()=>{},6623:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});var n=t(6551);function s(){let e=[{icon:Object(function(){var e=Error("Cannot find module '@/components/icons/ShippingIcons'");throw e.code="MODULE_NOT_FOUND",e}()),title:"Express Delivery",description:"Lightning-fast same-day and next-day delivery across major Egyptian cities.",features:["Same-day delivery in Cairo, Alexandria, and Giza","Next-day delivery to 50+ cities across Egypt","Real-time GPS tracking","Secure handling and insurance","Proof of delivery with signature","Temperature-controlled options"],pricing:"Starting from 50 EGP",deliveryTime:"4-24 hours"},{icon:Object(function(){var e=Error("Cannot find module '@/components/icons/ShippingIcons'");throw e.code="MODULE_NOT_FOUND",e}()),title:"Standard Shipping",description:"Reliable and cost-effective shipping solutions for everyday needs.",features:["2-5 business days delivery","Full insurance coverage included","Bulk shipping discounts","Flexible pickup scheduling","SMS and email notifications","Free packaging materials"],pricing:"Starting from 25 EGP",deliveryTime:"2-5 business days"},{icon:Object(function(){var e=Error("Cannot find module '@/components/icons/ShippingIcons'");throw e.code="MODULE_NOT_FOUND",e}()),title:"Freight Services",description:"Heavy cargo and bulk shipments via sea and land routes.",features:["Heavy cargo up to 50 tons","Port-to-port shipping","Custom clearance assistance","Warehousing solutions","Container shipping","Specialized equipment handling"],pricing:"Custom quotes",deliveryTime:"5-15 business days"},{icon:Object(function(){var e=Error("Cannot find module '@/components/icons/ShippingIcons'");throw e.code="MODULE_NOT_FOUND",e}()),title:"International Express",description:"Fast international shipping connecting Egypt with the world.",features:["Global network coverage","Express customs clearance","Door-to-door service","Document and parcel shipping","Duty and tax calculation","International tracking"],pricing:"Starting from 200 EGP",deliveryTime:"1-7 business days"}];return(0,n.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/layout/Header'");throw e.code="MODULE_NOT_FOUND",e}()),{}),(0,n.jsxs)("main",{children:[(0,n.jsx)("section",{className:"bg-gradient-to-br from-primary-600 to-primary-800 text-white py-20",children:(0,n.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,n.jsx)("div",{className:"flex justify-center mb-6",children:(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/icons/ShippingIcons'");throw e.code="MODULE_NOT_FOUND",e}()),{size:80,className:"text-accent-300"})}),(0,n.jsx)("h1",{className:"text-4xl lg:text-6xl font-display font-bold mb-6",children:"Our Services"}),(0,n.jsx)("p",{className:"text-xl text-primary-100 max-w-3xl mx-auto",children:"Comprehensive shipping and logistics solutions designed to meet all your delivery needs across Egypt and beyond."})]})}),(0,n.jsx)("section",{className:"py-20",children:(0,n.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,n.jsx)("div",{className:"grid lg:grid-cols-2 gap-8",children:e.map((e,r)=>(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/Card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"h-full",children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/Card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,n.jsxs)("div",{className:"flex items-center mb-4",children:[(0,n.jsx)("div",{className:"w-12 h-12 bg-primary-100 text-primary-600 rounded-lg flex items-center justify-center mr-4",children:(0,n.jsx)(e.icon,{size:24})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-2xl font-bold text-gray-900",children:e.title}),(0,n.jsx)("p",{className:"text-gray-600",children:e.description})]})]})}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/Card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,n.jsxs)("div",{className:"grid md:grid-cols-2 gap-6 mb-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-semibold text-gray-900 mb-3",children:"Features:"}),(0,n.jsx)("ul",{className:"space-y-2",children:e.features.map((e,r)=>(0,n.jsxs)("li",{className:"flex items-start text-sm text-gray-600",children:[(0,n.jsx)("svg",{className:"w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0",fill:"currentColor",viewBox:"0 0 20 20",children:(0,n.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),e]},r))})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-semibold text-gray-900 mb-1",children:"Pricing:"}),(0,n.jsx)("p",{className:"text-primary-600 font-medium",children:e.pricing})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-semibold text-gray-900 mb-1",children:"Delivery Time:"}),(0,n.jsx)("p",{className:"text-gray-600",children:e.deliveryTime})]}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/Button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"primary",className:"w-full",children:"Get Quote"})]})]})})]},r))})})}),(0,n.jsx)("section",{className:"py-20 bg-white",children:(0,n.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,n.jsxs)("div",{className:"text-center mb-16",children:[(0,n.jsx)("h2",{className:"text-3xl font-display font-bold text-gray-900 mb-4",children:"Additional Services"}),(0,n.jsx)("p",{className:"text-xl text-gray-600",children:"Comprehensive solutions to support your shipping needs"})]}),(0,n.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-6",children:[{title:"Packaging Services",description:"Professional packaging for fragile and valuable items",icon:"\uD83D\uDCE6"},{title:"Warehousing",description:"Secure storage solutions with inventory management",icon:"\uD83C\uDFE2"},{title:"Cash on Delivery",description:"COD services for e-commerce businesses",icon:"\uD83D\uDCB0"},{title:"Return Management",description:"Hassle-free return and exchange services",icon:"\uD83D\uDD04"}].map((e,r)=>(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/Card'");throw e.code="MODULE_NOT_FOUND",e}()),{hover:!0,className:"text-center",children:(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/Card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"p-6",children:[(0,n.jsx)("div",{className:"text-4xl mb-4",children:e.icon}),(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:e.title}),(0,n.jsx)("p",{className:"text-gray-600 text-sm",children:e.description})]})},r))})]})}),(0,n.jsx)("section",{className:"py-20 bg-gradient-to-r from-primary-600 to-accent-600",children:(0,n.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,n.jsx)("h2",{className:"text-3xl font-bold text-white mb-4",children:"Ready to Ship with Versa Tradez?"}),(0,n.jsx)("p",{className:"text-xl text-primary-100 mb-8",children:"Get started today and experience Egypt's most reliable shipping service."}),(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/Button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"secondary",size:"lg",children:"Get Free Quote"}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/Button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",size:"lg",className:"border-white text-white hover:bg-white hover:text-primary-600",children:"Contact Sales"})]})]})})]}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/layout/Footer'");throw e.code="MODULE_NOT_FOUND",e}()),{})]})}t(6770),!function(){var e=Error("Cannot find module '@/components/layout/Header'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/Footer'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/Card'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/Button'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/icons/ShippingIcons'");throw e.code="MODULE_NOT_FOUND",e}()},8371:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,7562,23)),Promise.resolve().then(t.t.bind(t,9100,23)),Promise.resolve().then(t.t.bind(t,6368,23)),Promise.resolve().then(t.t.bind(t,9907,23)),Promise.resolve().then(t.t.bind(t,9315,23)),Promise.resolve().then(t.t.bind(t,6715,23)),Promise.resolve().then(t.t.bind(t,7403,23)),Promise.resolve().then(t.t.bind(t,5133,23))},8619:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,1892,23)),Promise.resolve().then(t.t.bind(t,138,23)),Promise.resolve().then(t.t.bind(t,218,23)),Promise.resolve().then(t.t.bind(t,4349,23)),Promise.resolve().then(t.t.bind(t,2809,23)),Promise.resolve().then(t.t.bind(t,7569,23)),Promise.resolve().then(t.t.bind(t,4185,23)),Promise.resolve().then(t.t.bind(t,3243,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[252],()=>t(5184));module.exports=n})();