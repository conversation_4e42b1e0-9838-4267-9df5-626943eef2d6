'use client';

import React from 'react';
import { Card, CardContent } from '@/components/ui/Card';
import { TruckIcon, PackageIcon, ShipIcon, PlaneIcon } from '@/components/icons/ShippingIcons';

export const ServicesSection: React.FC = () => {
  const services = [
    {
      icon: TruckIcon,
      title: 'Express Delivery',
      description: 'Same-day and next-day delivery across major Egyptian cities including Cairo, Alexandria, and Giza.',
      features: ['Same-day delivery', 'Real-time tracking', 'Secure handling']
    },
    {
      icon: PackageIcon,
      title: 'Standard Shipping',
      description: 'Reliable and cost-effective shipping solutions for businesses and individuals throughout Egypt.',
      features: ['2-5 business days', 'Insurance included', 'Bulk discounts']
    },
    {
      icon: ShipIcon,
      title: 'Freight Services',
      description: 'Heavy cargo and bulk shipments via sea and land routes to all Egyptian ports and cities.',
      features: ['Heavy cargo', 'Port-to-port', 'Custom clearance']
    },
    {
      icon: PlaneIcon,
      title: 'International Express',
      description: 'Fast international shipping connecting Egypt with the world through our global network.',
      features: ['Global network', 'Express customs', 'Door-to-door']
    }
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl font-display font-bold text-gray-900 mb-4">
            Our Services
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Comprehensive shipping and courier solutions designed to meet all your delivery needs across Egypt and beyond.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {services.map((service, index) => (
            <Card key={index} hover className="h-full">
              <CardContent className="p-6 text-center">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-100 text-primary-600 rounded-full mb-6">
                  <service.icon size={32} />
                </div>
                
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {service.title}
                </h3>
                
                <p className="text-gray-600 mb-6">
                  {service.description}
                </p>
                
                <ul className="space-y-2">
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center text-sm text-gray-500">
                      <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      {feature}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <div className="bg-white rounded-2xl shadow-lg p-8 max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Need a Custom Solution?
            </h3>
            <p className="text-gray-600 mb-6">
              Our team can create tailored shipping solutions for your specific business needs.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors">
                Contact Sales
              </button>
              <button className="border border-primary-600 text-primary-600 px-6 py-3 rounded-lg hover:bg-primary-50 transition-colors">
                View All Services
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
